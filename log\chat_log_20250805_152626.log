2025-08-05 15:26:27.553 - chat_with_robot - chat_with_robot.py - <module> - line 921 - INFO - use_action: dont
2025-08-05 15:26:27.553 - chat_with_robot - chat_with_robot.py - <module> - line 922 - INFO - 
[启动HardwareAIAgent交互程序]

2025-08-05 15:26:27.559 - chat_with_robot - chat_with_robot.py - init_websocket - line 533 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1754378787559&accessNonce=fbcedc83-2615-4de5-b8d2-abb45d7354df&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=7fd831a1-71cd-11f0-9792-dc4546c07870&requestId=e33cbb2f-c241-4730-8b37-7c1c84a66a0b_joyinside&accessSign=c67615556c89d296108220d000671352, request_id: e33cbb2f-c241-4730-8b37-7c1c84a66a0b_joyinside
2025-08-05 15:26:27.560 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-08-05 15:26:27.560 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-08-05 15:26:27.957 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-08-05 15:26:28.420 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-08-05 15:26:30.223 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-08-05 15:26:30.224 - chat_with_robot - voice.py - _setup_audio_stream - line 339 - INFO - 使用音频设备: 1
2025-08-05 15:26:30.224 - chat_with_robot - voice.py - _setup_audio_stream - line 340 - INFO - channels: 4 <class 'int'>
2025-08-05 15:26:30.224 - chat_with_robot - voice.py - _setup_audio_stream - line 341 - INFO - rate: 44100.0 <class 'float'>
2025-08-05 15:26:30.306 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-08-05 15:26:30.306 - chat_with_robot - voice.py - init_wakeup - line 326 - INFO - 本地流式KWS检测器启动成功
2025-08-05 15:26:31.306 - chat_with_robot - chat_with_robot.py - play_audio - line 796 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 15:26:31.306 - chat_with_robot - chat_with_robot.py - play_audio - line 804 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
